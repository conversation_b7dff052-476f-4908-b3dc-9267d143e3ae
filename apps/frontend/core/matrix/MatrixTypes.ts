/**
 * 矩阵核心类型定义
 * 🎯 核心价值：统一的类型系统，支持数据驱动视图和业务模式切换
 * 📦 功能范围：矩阵数据、渲染配置、业务模式、计算属性
 * 🔄 架构设计：基于数据驱动的类型设计，支持高性能计算属性
 */

// ===== 基础矩阵类型 =====

/** 矩阵维度常量 */
export const MATRIX_SIZE = 33;
export const TOTAL_CELLS = MATRIX_SIZE * MATRIX_SIZE; // 1089

/** 坐标类型 */
export interface Coordinate {
  x: number;
  y: number;
}

/** 基础颜色类型（包含黑色） */
export type BasicColorType = 'black' | 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink';

/** 组类型 */
export type GroupType = 'A' | 'B' | 'C' | 'D' | 'E' | 'F' | 'G' | 'H' | 'I' | 'J' | 'K' | 'L' | 'M';

/** 数据级别 */
export type DataLevel = 1 | 2 | 3 | 4;

/** 业务模式 */
export type BusinessMode = 'coordinate' | 'color' | 'level' | 'word';

// ===== 颜色值定义 =====

/** 颜色值详细信息 */
export interface ColorValue {
  name: string;
  hex: string;
  rgb: [number, number, number];
  hsl: [number, number, number];
  mappingValue?: number; // 黑色没有mappingValue
}

// ===== 组偏移配置 =====

/** 组偏移配置 */
export interface GroupOffsetConfig {
  defaultOffset: [number, number];
  level1Offsets?: Record<BasicColorType, [number, number]>;
}

// ===== 单元格数据 =====

/** 单元格基础数据 */
export interface CellData {
  x: number;
  y: number;
  color?: BasicColorType;
  level?: DataLevel;
  value?: number;
  word?: string;
  isActive: boolean;
  isSelected: boolean;
  isHovered: boolean;
}

/** 单元格渲染数据 */
export interface CellRenderData {
  content: string;
  style: CellStyle;
  className: string;
  isInteractive: boolean;
}

/** 单元格样式 */
export interface CellStyle {
  backgroundColor?: string;
  color?: string;
  border?: string;
  opacity?: number;
  transform?: string;
  fontSize?: string;
  fontWeight?: string;
}

// ===== 矩阵状态 =====

/** 矩阵数据 */
export interface MatrixData {
  cells: Map<string, CellData>; // key: "x,y"
  selectedCells: Set<string>;
  hoveredCell: string | null;
  focusedCell: string | null;
}

/** 矩阵配置 */
export interface MatrixConfig {
  mode: BusinessMode;
}

// ===== 业务模式配置 =====

/** 业务模式处理器 */
export interface ModeHandler {
  processData: (data: MatrixData, config: MatrixConfig) => ProcessedMatrixData;
  renderCell: (cell: CellData, config: MatrixConfig) => CellRenderData;
  handleInteraction: (event: InteractionEvent, cell: CellData) => void;
}

/** 处理后的矩阵数据 */
export interface ProcessedMatrixData {
  cells: Map<string, CellData>;
  renderData: Map<string, CellRenderData>;
  metadata: {
    totalCells: number;
    activeCells: number;
    selectedCells: number;
    mode: BusinessMode;
  };
}

// ===== 交互事件 =====

/** 交互事件类型 */
export type InteractionEventType = 'click' | 'doubleClick' | 'hover' | 'focus' | 'keydown';

/** 交互事件 */
export interface InteractionEvent {
  type: InteractionEventType;
  coordinate: Coordinate;
  modifiers: {
    ctrl: boolean;
    shift: boolean;
    alt: boolean;
  };
  data?: any;
}

// ===== 计算属性 =====

/** 计算属性函数 */
export type ComputedProperty<T> = () => T;

/** 计算属性缓存 */
export interface ComputedCache {
  cellStyles: Map<string, CellStyle>;
  cellContents: Map<string, string>;
  cellClassNames: Map<string, string>;
  interactionStates: Map<string, boolean>;
  lastUpdate: number;
}



// ===== 性能监控 =====

/** 性能指标 */
export interface PerformanceMetrics {
  renderTime: number;
  updateTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  frameRate: number;
}

// ===== 工具类型 =====

/** 坐标键生成器 */
export const coordinateKey = (x: number, y: number): string => `${x},${y}`;

/** 坐标解析器 */
export const parseCoordinateKey = (key: string): Coordinate => {
  const [x, y] = key.split(',').map(Number);
  return { x, y };
};

/** 默认单元格数据 */
export const createDefaultCell = (x: number, y: number): CellData => ({
  x,
  y,
  isActive: true,
  isSelected: false,
  isHovered: false,
});

/** 默认矩阵配置 */
export const DEFAULT_MATRIX_CONFIG: MatrixConfig = {
  mode: 'coordinate',
};


